import { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Header from './components/Header';
import CourseCard from './components/CourseCard';
import Footer from './components/Footer';
import Loader, { CourseCardSkeleton, PageLoader } from './components/Loader';
import Pagination from './components/Pagination';
import InfiniteScroll from './components/InfiniteScroll';
import EmptyState from './components/EmptyState';
import { coursesData, getFilteredCourses } from './data/courses';

function App() {
  // State management
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const [displayMode, setDisplayMode] = useState('pagination'); // 'pagination' or 'infinite'
  const [displayedCourses, setDisplayedCourses] = useState([]);
  const [hasMore, setHasMore] = useState(true);

  const itemsPerPage = 8;

  // Filter courses based on search and category
  const filteredCourses = useMemo(() => {
    return getFilteredCourses(coursesData, searchTerm, selectedCategory);
  }, [searchTerm, selectedCategory]);

  // Pagination logic
  const totalPages = Math.ceil(filteredCourses.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedCourses = filteredCourses.slice(startIndex, startIndex + itemsPerPage);

  // Initial loading simulation
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  // Reset pagination when filters change
  useEffect(() => {
    setCurrentPage(1);
    if (displayMode === 'infinite') {
      setDisplayedCourses(filteredCourses.slice(0, itemsPerPage));
      setHasMore(filteredCourses.length > itemsPerPage);
    }
  }, [searchTerm, selectedCategory, displayMode, filteredCourses, itemsPerPage]);

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // Handle infinite scroll load more
  const handleLoadMore = () => {
    if (displayMode === 'infinite' && hasMore) {
      const nextItems = filteredCourses.slice(
        displayedCourses.length,
        displayedCourses.length + itemsPerPage
      );

      setDisplayedCourses(prev => [...prev, ...nextItems]);
      setHasMore(displayedCourses.length + nextItems.length < filteredCourses.length);
    }
  };

  // Clear all filters
  const handleClearFilters = () => {
    setSearchTerm('');
    setSelectedCategory('All');
    setCurrentPage(1);
  };

  // Get courses to display based on mode
  const coursesToDisplay = displayMode === 'infinite' ? displayedCourses : paginatedCourses;

  if (loading) {
    return <PageLoader />;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
      {/* Header */}
      <Header
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        selectedCategory={selectedCategory}
        setSelectedCategory={setSelectedCategory}
        courses={coursesData}
      />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Results Header */}
        <motion.div
          className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 space-y-4 sm:space-y-0"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              {searchTerm ? `Search results for "${searchTerm}"` :
               selectedCategory === 'All' ? 'All Courses' : `${selectedCategory} Courses`}
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {filteredCourses.length} course{filteredCourses.length !== 1 ? 's' : ''} found
            </p>
          </div>

          {/* Display Mode Toggle */}
          <div className="flex items-center space-x-2 bg-white dark:bg-gray-800 rounded-lg p-1 shadow-sm">
            <button
              onClick={() => setDisplayMode('pagination')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-300 ${
                displayMode === 'pagination'
                  ? 'bg-blue-500 text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              Pagination
            </button>
            <button
              onClick={() => setDisplayMode('infinite')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-300 ${
                displayMode === 'infinite'
                  ? 'bg-blue-500 text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              Infinite Scroll
            </button>
          </div>
        </motion.div>

        {/* Course Grid */}
        <AnimatePresence mode="wait">
          {filteredCourses.length === 0 ? (
            <EmptyState
              searchTerm={searchTerm}
              selectedCategory={selectedCategory}
              onClearFilters={handleClearFilters}
              type={searchTerm ? 'search' : selectedCategory !== 'All' ? 'category' : 'general'}
            />
          ) : (
            <motion.div
              key={`${searchTerm}-${selectedCategory}-${displayMode}`}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
            >
              {coursesToDisplay.map((course, index) => (
                <CourseCard
                  key={`${course.id}-${displayMode}`}
                  course={course}
                  index={index}
                />
              ))}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Pagination or Infinite Scroll */}
        {filteredCourses.length > 0 && (
          <>
            {displayMode === 'pagination' ? (
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
                itemsPerPage={itemsPerPage}
                totalItems={filteredCourses.length}
              />
            ) : (
              <InfiniteScroll
                hasMore={hasMore}
                loading={false}
                onLoadMore={handleLoadMore}
              />
            )}
          </>
        )}
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
}

export default App;
