import { motion } from 'framer-motion';
import { FiStar, FiClock, FiUsers, FiExternalLink, FiBookOpen } from 'react-icons/fi';
import { HiOutlineAcademicCap } from 'react-icons/hi';

const CourseCard = ({ course, index }) => {
  const handleEnrollClick = () => {
    window.open(course.courseUrl, '_blank', 'noopener,noreferrer');
  };

  const formatNumber = (num) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
  };

  return (
    <motion.div
      className="bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden group hover-lift"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.5,
        delay: (index % 8) * 0.1, // تحديد الـ delay بناءً على الموضع في الصفحة (8 كورسات كحد أقصى)
        ease: "easeOut"
      }}
      whileHover={{ y: -8 }}
    >
      {/* Course Image */}
      <div className="relative overflow-hidden">
        <motion.img
          src={course.image}
          alt={course.title}
          className="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110"
          loading="lazy"
        />
        
        {/* Badges */}
        <div className="absolute top-3 left-3 flex flex-col space-y-2">
          <span className="px-3 py-1 bg-blue-500 text-white text-xs font-medium rounded-full">
            {course.category}
          </span>
          {course.isFree && (
            <span className="px-2 py-1 bg-green-500 text-white text-xs font-medium rounded-full">
              FREE
            </span>
          )}
          {course.isOpenSource && (
            <span className="px-2 py-1 bg-purple-500 text-white text-xs font-medium rounded-full">
              Open Source
            </span>
          )}
        </div>

        {/* Level Badge */}
        <div className="absolute top-3 right-3">
          <span className="px-2 py-1 bg-black/70 text-white text-xs rounded-full backdrop-blur-sm">
            {course.level}
          </span>
        </div>

        {/* Overlay on hover */}
        <motion.div
          className="absolute inset-0 bg-black/50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
          initial={{ opacity: 0 }}
          whileHover={{ opacity: 1 }}
        >
          <motion.button
            onClick={handleEnrollClick}
            className="px-6 py-2 bg-white text-black rounded-lg font-medium hover:bg-gray-100 transition-colors flex items-center space-x-2"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <FiExternalLink className="w-4 h-4" />
            <span>Preview Course</span>
          </motion.button>
        </motion.div>
      </div>

      {/* Course Content */}
      <div className="p-4 sm:p-6">
        {/* Title */}
        <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
          {course.title}
        </h3>

        {/* Instructor and Platform */}
        <div className="mb-3 space-y-1">
          <p className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
            <HiOutlineAcademicCap className="w-4 h-4 mr-1 flex-shrink-0" />
            <span className="truncate">{course.instructor}</span>
          </p>
          {course.platform && (
            <p className="text-xs text-blue-600 dark:text-blue-400 font-medium">
              📚 {course.platform}
            </p>
          )}
        </div>

        {/* Description - Hidden on mobile */}
        <p className="hidden sm:block text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
          {course.description}
        </p>

        {/* Stats Row */}
        <div className="flex items-center justify-between mb-3 sm:mb-4 text-sm">
          {/* Rating */}
          <div className="flex items-center space-x-1">
            <FiStar className="w-4 h-4 text-yellow-400 fill-current" />
            <span className="font-medium">{course.rating}</span>
            <span className="text-gray-500 dark:text-gray-400 hidden sm:inline">
              ({formatNumber(course.reviewCount)})
            </span>
          </div>

          {/* Duration */}
          <div className="flex items-center space-x-1 text-gray-600 dark:text-gray-400">
            <FiClock className="w-4 h-4" />
            <span className="text-xs sm:text-sm">{course.duration}</span>
          </div>
        </div>

        {/* Enrollment Count - Hidden on mobile */}
        <div className="hidden sm:flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400 mb-4">
          <FiUsers className="w-4 h-4" />
          <span>{formatNumber(course.enrollmentCount)} students</span>
        </div>

        {/* Price and Enroll Button */}
        <div className="flex items-center justify-between">
          <div className="flex flex-col">
            <div className="flex items-center space-x-2">
              <span className="text-base sm:text-lg font-bold text-green-600 dark:text-green-400">
                {course.currentPrice || 'Free'}
              </span>
              {course.isUdemy && (
                <span className="px-2 py-1 bg-orange-500 text-white text-xs rounded-full">
                  Udemy
                </span>
              )}
            </div>
            <span className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 line-through">
              {course.originalPrice}
            </span>
            {course.couponCode && (
              <span className="text-xs text-blue-600 dark:text-blue-400 font-mono">
                Code: {course.couponCode}
              </span>
            )}
          </div>

          <motion.button
            onClick={handleEnrollClick}
            className="px-3 py-2 sm:px-6 sm:py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-300 flex items-center space-x-1 sm:space-x-2 shadow-lg hover:shadow-xl text-sm sm:text-base"
            whileHover={{
              scale: 1.05,
              boxShadow: "0 10px 25px rgba(59, 130, 246, 0.3)"
            }}
            whileTap={{ scale: 0.95 }}
          >
            <FiBookOpen className="w-4 h-4" />
            <span className="hidden sm:inline">
              {course.isUdemy ? 'Get Free' : 'Enroll Free'}
            </span>
            <span className="sm:hidden">
              {course.isUdemy ? 'Get' : 'Enroll'}
            </span>
          </motion.button>
        </div>

        {/* Last Updated and Coupon Validity - Hidden on mobile */}
        <div className="hidden sm:block mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>Updated: {new Date(course.lastUpdated).toLocaleDateString()}</span>
            {course.validUntil && (
              <span className="text-red-500 dark:text-red-400">
                Valid until: {new Date(course.validUntil).toLocaleDateString()}
              </span>
            )}
          </div>
        </div>

        {/* Mobile-only stats */}
        <div className="sm:hidden mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>{formatNumber(course.enrollmentCount)} students</span>
            <span>Updated: {new Date(course.lastUpdated).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}</span>
          </div>
          {course.validUntil && (
            <div className="mt-1 text-xs text-red-500 dark:text-red-400 text-center">
              ⏰ Valid until: {new Date(course.validUntil).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default CourseCard;
