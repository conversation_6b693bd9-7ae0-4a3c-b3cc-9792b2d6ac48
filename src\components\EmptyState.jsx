import { motion } from 'framer-motion';
import { FiSearch, FiBookOpen, FiRefreshCw } from 'react-icons/fi';

const EmptyState = ({ 
  searchTerm, 
  selectedCategory, 
  onClearFilters,
  type = 'search' // 'search', 'category', 'general'
}) => {
  const getEmptyStateContent = () => {
    switch (type) {
      case 'search':
        return {
          icon: FiSearch,
          title: `No courses found for "${searchTerm}"`,
          description: 'Try adjusting your search terms or browse our categories to discover amazing courses.',
          actionText: 'Clear Search',
          actionIcon: FiRefreshCw
        };
      case 'category':
        return {
          icon: FiBookOpen,
          title: `No courses in ${selectedCategory}`,
          description: 'This category is currently empty. Check back later or explore other categories.',
          actionText: 'View All Courses',
          actionIcon: FiRefreshCw
        };
      default:
        return {
          icon: FiBookOpen,
          title: 'No courses available',
          description: 'We\'re working hard to bring you amazing free courses. Check back soon!',
          actionText: 'Refresh Page',
          actionIcon: FiRefresh<PERSON>w
        };
    }
  };

  const content = getEmptyStateContent();
  const IconComponent = content.icon;
  const ActionIcon = content.actionIcon;

  const suggestions = [
    'Try different keywords',
    'Check your spelling',
    'Use more general terms',
    'Browse by category',
    'Explore trending courses'
  ];

  return (
    <motion.div
      className="flex flex-col items-center justify-center py-16 px-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      {/* Icon */}
      <motion.div
        className="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-6"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <IconComponent className="w-12 h-12 text-gray-400 dark:text-gray-600" />
      </motion.div>

      {/* Title */}
      <motion.h2
        className="text-2xl font-bold text-gray-900 dark:text-white mb-3 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        {content.title}
      </motion.h2>

      {/* Description */}
      <motion.p
        className="text-gray-600 dark:text-gray-400 text-center max-w-md mb-8"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        {content.description}
      </motion.p>

      {/* Suggestions for search */}
      {type === 'search' && (
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3 text-center">
            Search suggestions:
          </h3>
          <ul className="space-y-2">
            {suggestions.map((suggestion, index) => (
              <motion.li
                key={index}
                className="flex items-center text-sm text-gray-600 dark:text-gray-400"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: 0.6 + index * 0.1 }}
              >
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-3" />
                {suggestion}
              </motion.li>
            ))}
          </ul>
        </motion.div>
      )}

      {/* Action Button */}
      <motion.button
        onClick={onClearFilters}
        className="inline-flex items-center px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-300 space-x-2"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.7 }}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <ActionIcon className="w-5 h-5" />
        <span>{content.actionText}</span>
      </motion.button>

      {/* Decorative Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-blue-200 dark:bg-blue-900 rounded-full opacity-20"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>
    </motion.div>
  );
};

export default EmptyState;
