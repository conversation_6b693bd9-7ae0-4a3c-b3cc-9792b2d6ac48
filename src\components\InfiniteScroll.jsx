import { useEffect, useRef } from 'react';
import { useInView } from 'react-intersection-observer';
import { motion } from 'framer-motion';
import Loader from './Loader';

const InfiniteScroll = ({ 
  hasMore, 
  loading, 
  onLoadMore, 
  threshold = 0.1 
}) => {
  const { ref, inView } = useInView({
    threshold,
    triggerOnce: false,
  });

  const loadingRef = useRef(false);

  useEffect(() => {
    if (inView && hasMore && !loading && !loadingRef.current) {
      loadingRef.current = true;
      onLoadMore();
      
      // Reset loading ref after a short delay
      setTimeout(() => {
        loadingRef.current = false;
      }, 1000);
    }
  }, [inView, hasMore, loading, onLoadMore]);

  if (!hasMore && !loading) {
    return (
      <motion.div
        className="text-center py-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full mb-4">
          <svg
            className="w-8 h-8 text-gray-400 dark:text-gray-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          You've reached the end!
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          You've seen all available courses. Check back later for new content.
        </p>
      </motion.div>
    );
  }

  return (
    <div ref={ref} className="flex justify-center py-8">
      {loading && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          transition={{ duration: 0.3 }}
        >
          <Loader size="medium" text="Loading more courses..." />
        </motion.div>
      )}
    </div>
  );
};

export default InfiniteScroll;
