// استيراد خدمات الكورسات الحقيقية
import { fetchCoursesFromAPI, categories as apiCategories } from '../services/courseService.js';
import { fetchRealFreeCourses } from '../services/realCourseAPI.js';

export const categories = [
  'All',
  'Programming',
  'Web Development',
  'Data Science',
  'Machine Learning',
  'Design',
  'Business',
  'Marketing',
  'Photography',
  'Language Learning',
  'Mathematics',
  'Science'
];

export const coursesData = [
  {
    id: 1,
    title: "Complete React Developer Course 2024",
    instructor: "<PERSON>",
    image: "https://img-c.udemycdn.com/course/750x422/1362070_b9a1_2.jpg",
    rating: 4.8,
    reviewCount: 15420,
    duration: "40 hours",
    category: "Development",
    description: "Learn React from scratch with hooks, context, and modern patterns",
    originalPrice: "$199",
    enrollmentCount: 45000,
    level: "Beginner to Advanced",
    language: "English",
    lastUpdated: "2024-01-15",
    courseUrl: "https://www.udemy.com/course/react-complete-guide/"
  },
  {
    id: 2,
    title: "UI/UX Design Masterclass",
    instructor: "<PERSON>",
    image: "https://img-c.udemycdn.com/course/750x422/1565838_e54e_18.jpg",
    rating: 4.7,
    reviewCount: 8930,
    duration: "25 hours",
    category: "Design",
    description: "Master UI/UX design principles with Figma and Adobe XD",
    originalPrice: "$149",
    enrollmentCount: 32000,
    level: "Intermediate",
    language: "English",
    lastUpdated: "2024-01-10",
    courseUrl: "https://www.udemy.com/course/ui-ux-design-tutorial/"
  },
  {
    id: 3,
    title: "Digital Marketing Strategy 2024",
    instructor: "Mohamed Ali",
    image: "https://img-c.udemycdn.com/course/750x422/1426570_1b91_3.jpg",
    rating: 4.6,
    reviewCount: 12500,
    duration: "30 hours",
    category: "Marketing",
    description: "Complete digital marketing course covering SEO, SEM, and social media",
    originalPrice: "$179",
    enrollmentCount: 28000,
    level: "Beginner",
    language: "English",
    lastUpdated: "2024-01-08",
    courseUrl: "https://www.udemy.com/course/digital-marketing-course/"
  },
  {
    id: 4,
    title: "Python for Data Science",
    instructor: "Dr. Fatima Nour",
    image: "https://img-c.udemycdn.com/course/750x422/1754098_e0df_3.jpg",
    rating: 4.9,
    reviewCount: 22100,
    duration: "45 hours",
    category: "Development",
    description: "Learn Python programming for data analysis and machine learning",
    originalPrice: "$199",
    enrollmentCount: 67000,
    level: "Beginner to Intermediate",
    language: "English",
    lastUpdated: "2024-01-12",
    courseUrl: "https://www.udemy.com/course/python-for-data-science-and-machine-learning-bootcamp/"
  },
  {
    id: 5,
    title: "Photography Fundamentals",
    instructor: "Omar Khaled",
    image: "https://img-c.udemycdn.com/course/750x422/1463348_52a4_4.jpg",
    rating: 4.5,
    reviewCount: 6780,
    duration: "20 hours",
    category: "Photography",
    description: "Master the basics of photography with composition and lighting techniques",
    originalPrice: "$129",
    enrollmentCount: 18500,
    level: "Beginner",
    language: "English",
    lastUpdated: "2024-01-05",
    courseUrl: "https://www.udemy.com/course/photography-masterclass-complete-guide-to-photography/"
  },
  {
    id: 6,
    title: "Business Strategy & Leadership",
    instructor: "Layla Hassan",
    image: "https://img-c.udemycdn.com/course/750x422/1430260_8fc7_7.jpg",
    rating: 4.4,
    reviewCount: 9200,
    duration: "35 hours",
    category: "Business",
    description: "Develop strategic thinking and leadership skills for modern business",
    originalPrice: "$169",
    enrollmentCount: 24000,
    level: "Intermediate to Advanced",
    language: "English",
    lastUpdated: "2024-01-03",
    courseUrl: "https://www.udemy.com/course/business-strategy/"
  },
  {
    id: 7,
    title: "Music Production with Ableton Live",
    instructor: "Karim Mostafa",
    image: "https://img-c.udemycdn.com/course/750x422/1565840_7f3a_12.jpg",
    rating: 4.7,
    reviewCount: 5400,
    duration: "28 hours",
    category: "Music",
    description: "Create professional music tracks using Ableton Live",
    originalPrice: "$159",
    enrollmentCount: 15000,
    level: "Beginner to Intermediate",
    language: "English",
    lastUpdated: "2024-01-01",
    courseUrl: "https://www.udemy.com/course/music-production-in-ableton-live-9/"
  },
  {
    id: 8,
    title: "Complete Yoga & Meditation Guide",
    instructor: "Nadia Ibrahim",
    image: "https://img-c.udemycdn.com/course/750x422/1436288_668a_5.jpg",
    rating: 4.8,
    reviewCount: 7800,
    duration: "15 hours",
    category: "Health & Fitness",
    description: "Transform your life with yoga and meditation practices",
    originalPrice: "$99",
    enrollmentCount: 21000,
    level: "All Levels",
    language: "English",
    lastUpdated: "2023-12-28",
    courseUrl: "https://www.udemy.com/course/yoga-meditation-course/"
  }
];

export const getFilteredCourses = (courses, searchTerm, selectedCategory) => {
  return courses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.instructor.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === 'All' || course.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });
};

// دالة لجلب جميع الكورسات (حقيقية + وهمية)
export const fetchAllCourses = async () => {
  try {
    console.log('🔄 جاري جلب الكورسات من المصادر المختلفة...');

    // جلب الكورسات من مصادر متعددة
    const [apiCourses, realCourses] = await Promise.allSettled([
      fetchCoursesFromAPI(),
      fetchRealFreeCourses()
    ]);

    let allCourses = [...coursesData]; // البيانات الوهمية كـ backup

    // إضافة الكورسات من API
    if (apiCourses.status === 'fulfilled' && apiCourses.value) {
      console.log(`✅ تم جلب ${apiCourses.value.length} كورس من API`);
      allCourses = [...allCourses, ...apiCourses.value];
    }

    // إضافة الكورسات الحقيقية
    if (realCourses.status === 'fulfilled' && realCourses.value) {
      console.log(`✅ تم جلب ${realCourses.value.length} كورس حقيقي`);
      allCourses = [...allCourses, ...realCourses.value];
    }

    // إزالة المكررات بناءً على العنوان
    const uniqueCourses = allCourses.filter((course, index, self) =>
      index === self.findIndex(c => c.title.toLowerCase() === course.title.toLowerCase())
    );

    console.log(`🎉 إجمالي الكورسات المتاحة: ${uniqueCourses.length}`);
    return uniqueCourses;

  } catch (error) {
    console.error('❌ خطأ في جلب الكورسات:', error);
    // في حالة الفشل، استخدم البيانات الوهمية
    return coursesData;
  }
};

export const getSuggestedCourses = (searchTerm, courses) => {
  if (!searchTerm) return [];

  return courses
    .filter(course =>
      course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.instructor.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (course.platform && course.platform.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .slice(0, 5);
};
