// استيراد خدمات الكورسات الحقيقية
import { fetchCoursesFromAPI } from '../services/courseService.js';
import { fetchRealFreeCourses } from '../services/realCourseAPI.js';

export const categories = [
  'All',
  'Programming',
  'Web Development',
  'Data Science',
  'Machine Learning',
  'Design',
  'Business',
  'Marketing',
  'Photography',
  'Language Learning',
  'Mathematics',
  'Science'
];

// بيانات احتياطية في حالة فشل جلب البيانات من APIs
export const coursesData = [
  {
    id: 'backup-1',
    title: "Complete React Developer Course 2024",
    instructor: "<PERSON>",
    platform: "Online Learning",
    image: "https://img-c.udemycdn.com/course/750x422/1362070_b9a1_2.jpg",
    rating: 4.8,
    reviewCount: 15420,
    duration: "40 hours",
    category: "Web Development",
    description: "Learn React from scratch with hooks, context, and modern patterns",
    originalPrice: "$199",
    currentPrice: "Free",
    enrollmentCount: 45000,
    level: "Beginner to Advanced",
    language: "English",
    lastUpdated: "2024-01-15",
    courseUrl: "https://www.udemy.com/course/react-complete-guide/",
    isFree: true
  }
];

export const getFilteredCourses = (courses, searchTerm, selectedCategory) => {
  return courses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.instructor.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === 'All' || course.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });
};

// دالة لجلب جميع الكورسات (حقيقية + وهمية)
export const fetchAllCourses = async () => {
  try {
    console.log('🔄 جاري جلب الكورسات من المصادر المختلفة...');

    // جلب الكورسات من مصادر متعددة
    const [apiCourses, realCourses] = await Promise.allSettled([
      fetchCoursesFromAPI(),
      fetchRealFreeCourses()
    ]);

    let allCourses = [...coursesData]; // البيانات الوهمية كـ backup

    // إضافة الكورسات من API
    if (apiCourses.status === 'fulfilled' && apiCourses.value) {
      console.log(`✅ تم جلب ${apiCourses.value.length} كورس من API`);
      allCourses = [...allCourses, ...apiCourses.value];
    }

    // إضافة الكورسات الحقيقية
    if (realCourses.status === 'fulfilled' && realCourses.value) {
      console.log(`✅ تم جلب ${realCourses.value.length} كورس حقيقي`);
      allCourses = [...allCourses, ...realCourses.value];
    }

    // إزالة المكررات بناءً على العنوان
    const uniqueCourses = allCourses.filter((course, index, self) =>
      index === self.findIndex(c => c.title.toLowerCase() === course.title.toLowerCase())
    );

    console.log(`🎉 إجمالي الكورسات المتاحة: ${uniqueCourses.length}`);
    return uniqueCourses;

  } catch (error) {
    console.error('❌ خطأ في جلب الكورسات:', error);
    // في حالة الفشل، استخدم البيانات الوهمية
    return coursesData;
  }
};

export const getSuggestedCourses = (searchTerm, courses) => {
  if (!searchTerm) return [];

  return courses
    .filter(course =>
      course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.instructor.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (course.platform && course.platform.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .slice(0, 5);
};
