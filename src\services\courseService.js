// Course Service - يجمع الكورسات المجانية من مصادر متعددة

const CORS_PROXY = 'https://api.allorigins.win/raw?url=';

// مصادر الكورسات المجانية
const COURSE_SOURCES = {
  // Coursera Free Courses
  COURSERA_FREE: 'https://www.coursera.org/api/courses.v1?q=search&query=free&limit=50',
  
  // edX Free Courses  
  EDX_FREE: 'https://courses.edx.org/api/courses/v1/courses/?page_size=50',
  
  // FreeCodeCamp
  FREECODECAMP: 'https://www.freecodecamp.org/api/challenges',
  
  // Khan Academy (محاكاة)
  KHAN_ACADEMY: 'https://www.khanacademy.org/api/v1/topics/topictree',
};

// فئات الكورسات
export const categories = [
  'All',
  'Programming',
  'Web Development', 
  'Data Science',
  'Machine Learning',
  'Design',
  'Business',
  'Marketing',
  'Photography',
  'Language Learning',
  'Mathematics',
  'Science'
];

// دالة لجلب الكورسات من مصادر مختلفة
export const fetchCoursesFromAPI = async () => {
  try {
    const courses = [];
    
    // جلب كورسات من مصادر متعددة
    const coursePromises = [
      fetchCourseraFreeCourses(),
      fetchEdXFreeCourses(),
      fetchFreeCodeCampCourses(),
      fetchKhanAcademyCourses(),
      fetchMockFreeCourses() // كورسات وهمية كـ backup
    ];

    const results = await Promise.allSettled(coursePromises);
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        courses.push(...result.value);
      } else {
        console.warn(`Failed to fetch from source ${index}:`, result.reason);
      }
    });

    // ترتيب الكورسات حسب التقييم والشعبية
    return courses.sort((a, b) => {
      const scoreA = (a.rating || 0) * (a.enrollmentCount || 0);
      const scoreB = (b.rating || 0) * (b.enrollmentCount || 0);
      return scoreB - scoreA;
    });

  } catch (error) {
    console.error('Error fetching courses:', error);
    // في حالة فشل جميع المصادر، استخدم الكورسات الوهمية
    return fetchMockFreeCourses();
  }
};

// جلب كورسات Coursera المجانية
const fetchCourseraFreeCourses = async () => {
  try {
    // محاكاة API Coursera (لأن API الحقيقي يتطلب مفاتيح)
    const mockCourseraData = [
      {
        id: 'coursera-1',
        title: 'Machine Learning by Stanford University',
        instructor: 'Andrew Ng',
        platform: 'Coursera',
        image: 'https://d3c33hcgiwev3.cloudfront.net/imageAssetProxy.v1/ML-Tuts-1_edX.jpg',
        rating: 4.9,
        reviewCount: 180000,
        duration: '11 weeks',
        category: 'Machine Learning',
        description: 'Learn about the most effective machine learning techniques',
        originalPrice: 'Free',
        enrollmentCount: 4200000,
        level: 'Beginner',
        language: 'English',
        lastUpdated: '2024-01-15',
        courseUrl: 'https://www.coursera.org/learn/machine-learning',
        isFree: true
      },
      {
        id: 'coursera-2', 
        title: 'Python for Everybody Specialization',
        instructor: 'Charles Severance',
        platform: 'Coursera',
        image: 'https://d3c33hcgiwev3.cloudfront.net/imageAssetProxy.v1/python-logo.png',
        rating: 4.8,
        reviewCount: 95000,
        duration: '8 months',
        category: 'Programming',
        description: 'Learn to Program and Analyze Data with Python',
        originalPrice: 'Free',
        enrollmentCount: 1800000,
        level: 'Beginner',
        language: 'English',
        lastUpdated: '2024-01-10',
        courseUrl: 'https://www.coursera.org/specializations/python',
        isFree: true
      }
    ];
    
    return mockCourseraData;
  } catch (error) {
    console.error('Error fetching Coursera courses:', error);
    return [];
  }
};

// جلب كورسات edX المجانية
const fetchEdXFreeCourses = async () => {
  try {
    const mockEdXData = [
      {
        id: 'edx-1',
        title: 'Introduction to Computer Science - CS50x',
        instructor: 'David Malan',
        platform: 'edX',
        image: 'https://prod-discovery.edx-cdn.org/media/course/image/cs50.jpg',
        rating: 4.7,
        reviewCount: 125000,
        duration: '12 weeks',
        category: 'Programming',
        description: 'Harvard University\'s introduction to computer science',
        originalPrice: 'Free',
        enrollmentCount: 3500000,
        level: 'Beginner',
        language: 'English',
        lastUpdated: '2024-01-12',
        courseUrl: 'https://www.edx.org/course/introduction-computer-science-harvardx-cs50x',
        isFree: true
      },
      {
        id: 'edx-2',
        title: 'Introduction to Data Science',
        instructor: 'MIT Faculty',
        platform: 'edX',
        image: 'https://prod-discovery.edx-cdn.org/media/course/image/data-science.jpg',
        rating: 4.6,
        reviewCount: 78000,
        duration: '10 weeks',
        category: 'Data Science',
        description: 'Learn the fundamentals of data science',
        originalPrice: 'Free',
        enrollmentCount: 890000,
        level: 'Intermediate',
        language: 'English',
        lastUpdated: '2024-01-08',
        courseUrl: 'https://www.edx.org/course/introduction-to-data-science',
        isFree: true
      }
    ];
    
    return mockEdXData;
  } catch (error) {
    console.error('Error fetching edX courses:', error);
    return [];
  }
};

// جلب كورسات FreeCodeCamp
const fetchFreeCodeCampCourses = async () => {
  try {
    const mockFCCData = [
      {
        id: 'fcc-1',
        title: 'Responsive Web Design Certification',
        instructor: 'FreeCodeCamp',
        platform: 'FreeCodeCamp',
        image: 'https://cdn.freecodecamp.org/platform/universal/fcc_primary.svg',
        rating: 4.8,
        reviewCount: 250000,
        duration: '300 hours',
        category: 'Web Development',
        description: 'Learn HTML, CSS, and responsive web design principles',
        originalPrice: 'Free',
        enrollmentCount: 2100000,
        level: 'Beginner',
        language: 'English',
        lastUpdated: '2024-01-14',
        courseUrl: 'https://www.freecodecamp.org/learn/responsive-web-design/',
        isFree: true
      },
      {
        id: 'fcc-2',
        title: 'JavaScript Algorithms and Data Structures',
        instructor: 'FreeCodeCamp',
        platform: 'FreeCodeCamp', 
        image: 'https://cdn.freecodecamp.org/platform/universal/fcc_primary.svg',
        rating: 4.7,
        reviewCount: 180000,
        duration: '300 hours',
        category: 'Programming',
        description: 'Learn JavaScript fundamentals and algorithmic thinking',
        originalPrice: 'Free',
        enrollmentCount: 1650000,
        level: 'Intermediate',
        language: 'English',
        lastUpdated: '2024-01-11',
        courseUrl: 'https://www.freecodecamp.org/learn/javascript-algorithms-and-data-structures/',
        isFree: true
      }
    ];
    
    return mockFCCData;
  } catch (error) {
    console.error('Error fetching FreeCodeCamp courses:', error);
    return [];
  }
};

// جلب كورسات Khan Academy
const fetchKhanAcademyCourses = async () => {
  try {
    const mockKhanData = [
      {
        id: 'khan-1',
        title: 'Intro to Programming: Drawing & Animation',
        instructor: 'Khan Academy',
        platform: 'Khan Academy',
        image: 'https://cdn.kastatic.org/images/khan-logo-dark-background.png',
        rating: 4.5,
        reviewCount: 45000,
        duration: '15 hours',
        category: 'Programming',
        description: 'Learn programming basics through drawing and animation',
        originalPrice: 'Free',
        enrollmentCount: 750000,
        level: 'Beginner',
        language: 'English',
        lastUpdated: '2024-01-09',
        courseUrl: 'https://www.khanacademy.org/computing/computer-programming',
        isFree: true
      }
    ];
    
    return mockKhanData;
  } catch (error) {
    console.error('Error fetching Khan Academy courses:', error);
    return [];
  }
};

// كورسات وهمية كـ backup
const fetchMockFreeCourses = async () => {
  return [
    {
      id: 'backup-1',
      title: 'Complete Web Development Bootcamp',
      instructor: 'Tech Academy',
      platform: 'Online Learning',
      image: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=500',
      rating: 4.6,
      reviewCount: 12500,
      duration: '40 hours',
      category: 'Web Development',
      description: 'Learn HTML, CSS, JavaScript, and modern web frameworks',
      originalPrice: 'Free',
      enrollmentCount: 85000,
      level: 'Beginner to Advanced',
      language: 'English',
      lastUpdated: '2024-01-15',
      courseUrl: '#',
      isFree: true
    }
  ];
};

// دالة البحث والفلترة
export const getFilteredCourses = (courses, searchTerm, selectedCategory) => {
  return courses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.instructor.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.platform.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === 'All' || course.category === selectedCategory;
    
    return matchesSearch && matchesCategory && course.isFree;
  });
};

// دالة الاقتراحات
export const getSuggestedCourses = (searchTerm, courses) => {
  if (!searchTerm) return [];
  
  return courses
    .filter(course => 
      course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.instructor.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.platform.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .slice(0, 5);
};
